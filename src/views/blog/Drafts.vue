<template>
  <div class="drafts-page">
    <div class="container">
      <div class="page-header">
        <h1><i class="el-icon-collection"></i> 我的草稿</h1>
        <p>这里是你保存的草稿文章</p>
      </div>

      <div class="drafts-list">
        <el-row v-if="drafts.length > 0" :gutter="20">
          <el-col v-for="draft in drafts" :key="draft.id" :lg="8" :md="12" :sm="24">
            <el-card class="draft-card" @click.native="editDraft(draft)">
              <div class="draft-header">
                <h3 class="draft-title">{{ draft.title || '无标题' }}</h3>
                <el-dropdown trigger="click" @command="handleDraftCommand" @click.native.stop>
                  <i class="el-icon-more"></i>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="{ action: 'edit', draft }">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'duplicate', draft }"
                      >复制</el-dropdown-item
                    >
                    <el-dropdown-item :command="{ action: 'delete', draft }" divided
                      >删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>

              <div class="draft-excerpt">
                {{ draft.excerpt || draft.content.substring(0, 100) + '...' }}
              </div>

              <div class="draft-meta">
                <span><i class="el-icon-time"></i> {{ formatDate(draft.updatedAt) }}</span>
                <span><i class="el-icon-document"></i> {{ countWords(draft.content) }} 字</span>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div v-else class="empty-state">
          <i class="el-icon-document-add"></i>
          <h3>暂无草稿</h3>
          <p>开始写作你的第一篇文章吧</p>
          <el-button type="primary" @click="$router.push('/editor')">开始写作</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { countWords } from '@/utils/editor';

export default {
  name: 'Drafts',
  data() {
    return {
      drafts: [],
    };
  },

  created() {
    this.loadDrafts();
  },

  methods: {
    async loadDrafts() {
      try {
        // 模拟 API 调用
        const response = await this.$http.get('/api/articles?status=draft');
        this.drafts = response.data;
      } catch (error) {
        this.$message.error('加载草稿失败');
      }
    },

    editDraft(draft) {
      this.$router.push(`/editor/${draft.id}`);
    },

    handleDraftCommand({ action, draft }) {
      switch (action) {
        case 'edit':
          this.editDraft(draft);
          break;
        case 'duplicate':
          this.duplicateDraft(draft);
          break;
        case 'delete':
          this.deleteDraft(draft);
          break;
      }
    },

    async duplicateDraft(draft) {
      try {
        const newDraft = {
          ...draft,
          id: undefined,
          title: `${draft.title} - 副本`,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        await this.$http.post('/api/articles', newDraft);
        this.$message.success('草稿已复制');
        this.loadDrafts();
      } catch (error) {
        this.$message.error('复制失败');
      }
    },

    deleteDraft(draft) {
      this.$confirm(`确定删除草稿 "${draft.title || '无标题'}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            await this.$http.delete(`/api/articles/${draft.id}`);
            this.$message.success('草稿已删除');
            this.loadDrafts();
          } catch (error) {
            this.$message.error('删除失败');
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    countWords,

    formatDate(date) {
      return new Date(date).toLocaleDateString();
    },
  },
};
</script>

<style scoped>
.drafts-page {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  margin: 0 0 10px;
  color: #333;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.draft-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.draft-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.draft-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.draft-title {
  font-size: 1.2rem;
  margin: 0;
  color: #333;
  flex: 1;
  margin-right: 10px;
}

.draft-excerpt {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.draft-meta {
  display: flex;
  justify-content: space-between;
  color: #999;
  font-size: 0.9rem;
}

.draft-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-state i {
  font-size: 4rem;
  color: #ddd;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px;
  font-size: 1.5rem;
}
</style>
