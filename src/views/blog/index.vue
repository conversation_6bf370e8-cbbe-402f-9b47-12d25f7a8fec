<template>
  <div class="blog-editor">
    <div class="editor-header">
      <div class="container">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">
              <i class="el-icon-edit"></i>
              {{ isEdit ? '编辑文章' : '写文章' }}
            </h1>
            <p class="page-subtitle">
              {{ isEdit ? '修改你的文章内容' : '分享你的想法和见解' }}
            </p>
          </div>

          <div class="action-buttons">
            <el-button :loading="saving" icon="el-icon-collection" @click="saveDraft">
              保存草稿
            </el-button>
            <el-button icon="el-icon-view" @click="previewArticle"> 预览 </el-button>
            <el-button
              type="primary"
              :loading="publishing"
              icon="el-icon-upload2"
              @click="publishArticle"
            >
              {{ isEdit ? '更新文章' : '发布文章' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="editor-container">
      <div class="container">
        <el-row :gutter="20">
          <!-- 主编辑区域 -->
          <el-col :lg="16" :md="24">
            <div class="main-editor">
              <!-- 文章基本信息 -->
              <el-card class="info-card">
                <div slot="header">
                  <i class="el-icon-document"></i>
                  文章信息
                </div>

                <el-form
                  ref="articleForm"
                  :model="articleForm"
                  :rules="formRules"
                  label-width="80px"
                >
                  <el-form-item label="文章标题" prop="title">
                    <el-input
                      v-model="articleForm.title"
                      placeholder="请输入文章标题"
                      maxlength="100"
                      show-word-limit
                      size="large"
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="文章摘要" prop="excerpt">
                    <el-input
                      v-model="articleForm.excerpt"
                      type="textarea"
                      placeholder="请输入文章摘要，建议100-200字"
                      :rows="3"
                      maxlength="500"
                      show-word-limit
                    >
                    </el-input>
                  </el-form-item>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="分类" prop="category">
                        <el-select
                          v-model="articleForm.category"
                          placeholder="选择分类"
                          style="width: 100%"
                        >
                          <el-option
                            v-for="cat in categories"
                            :key="cat.value"
                            :label="cat.label"
                            :value="cat.value"
                          >
                            <i :class="cat.icon"></i>
                            {{ cat.label }}
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="阅读时间">
                        <el-input
                          v-model.number="articleForm.readTime"
                          type="number"
                          placeholder="分钟"
                        >
                          <template slot="append">分钟</template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-form-item label="标签">
                    <div class="tags-input">
                      <el-tag
                        v-for="tag in articleForm.tags"
                        :key="tag"
                        closable
                        class="tag-item"
                        @close="removeTag(tag)"
                      >
                        {{ tag }}
                      </el-tag>

                      <el-input
                        v-if="tagInputVisible"
                        ref="tagInput"
                        v-model="tagInputValue"
                        size="small"
                        class="tag-input"
                        @keyup.enter.native="addTag"
                        @blur="addTag"
                      >
                      </el-input>

                      <el-button v-else size="small" class="add-tag-btn" @click="showTagInput">
                        <i class="el-icon-plus"></i> 添加标签
                      </el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </el-card>

              <!-- 封面图片上传 -->
              <el-card class="cover-card">
                <div slot="header">
                  <i class="el-icon-picture"></i>
                  文章封面
                </div>

                <div class="cover-upload">
                  <el-upload
                    class="cover-uploader"
                    :action="uploadUrl"
                    :show-file-list="false"
                    :before-upload="beforeCoverUpload"
                    :on-success="handleCoverSuccess"
                    :on-error="handleUploadError"
                    :headers="uploadHeaders"
                    accept="image/*"
                    drag
                  >
                    <div v-if="articleForm.coverImage" class="cover-preview">
                      <img :src="articleForm.coverImage" alt="封面预览" />
                      <div class="cover-overlay">
                        <i class="el-icon-edit-outline"></i>
                        <span>点击更换封面</span>
                      </div>
                    </div>

                    <div v-else class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                      <div class="upload-text">
                        <p>点击或拖拽上传封面图片</p>
                        <p class="upload-tip">支持 JPG、PNG 格式，建议尺寸 800×400</p>
                      </div>
                    </div>
                  </el-upload>

                  <div v-if="articleForm.coverImage" class="cover-actions">
                    <el-button size="small" icon="el-icon-delete" @click="removeCover">
                      移除封面
                    </el-button>
                  </div>
                </div>
              </el-card>

              <!-- 文章编辑器 -->
              <el-card class="editor-card">
                <div slot="header" class="editor-header-content">
                  <div>
                    <i class="el-icon-edit-outline"></i>
                    文章内容
                  </div>
                  <div class="editor-tools">
                    <el-button-group>
                      <el-button
                        size="small"
                        :type="editorMode === 'wysiwyg' ? 'primary' : ''"
                        @click="switchEditorMode('wysiwyg')"
                      >
                        富文本
                      </el-button>
                      <el-button
                        size="small"
                        :type="editorMode === 'markdown' ? 'primary' : ''"
                        @click="switchEditorMode('markdown')"
                      >
                        Markdown
                      </el-button>
                    </el-button-group>
                  </div>
                </div>

                <!-- 富文本编辑器 -->
                <div v-if="editorMode === 'wysiwyg'" class="wysiwyg-editor">
                  <div class="editor-toolbar">
                    <el-button-group>
                      <el-button size="small" title="粗体" @click="execCommand('bold')">
                        <b>B</b>
                      </el-button>
                      <el-button size="small" title="斜体" @click="execCommand('italic')">
                        <i>I</i>
                      </el-button>
                      <el-button size="small" title="下划线" @click="execCommand('underline')">
                        <u>U</u>
                      </el-button>
                    </el-button-group>

                    <el-button-group>
                      <el-button size="small" title="标题1" @click="insertHeading('h1')"
                        >H1</el-button
                      >
                      <el-button size="small" title="标题2" @click="insertHeading('h2')"
                        >H2</el-button
                      >
                      <el-button size="small" title="标题3" @click="insertHeading('h3')"
                        >H3</el-button
                      >
                    </el-button-group>

                    <el-button-group>
                      <el-button
                        size="small"
                        title="无序列表"
                        @click="execCommand('insertUnorderedList')"
                      >
                        <i class="el-icon-menu"></i>
                      </el-button>
                      <el-button
                        size="small"
                        title="有序列表"
                        @click="execCommand('insertOrderedList')"
                      >
                        <i class="el-icon-tickets"></i>
                      </el-button>
                      <el-button size="small" title="插入链接" @click="insertLink">
                        <i class="el-icon-link"></i>
                      </el-button>
                    </el-button-group>

                    <el-button-group>
                      <el-button size="small" title="插入图片" @click="insertImage">
                        <i class="el-icon-picture"></i>
                      </el-button>
                      <el-button size="small" title="代码块" @click="insertCode">
                        <i class="el-icon-document-copy"></i>
                      </el-button>
                    </el-button-group>
                  </div>

                  <div
                    ref="wysiwygEditor"
                    class="wysiwyg-content"
                    contenteditable="true"
                    placeholder="开始写作..."
                    @input="handleWysiwygInput"
                    @paste="handlePaste"
                  ></div>
                </div>

                <!-- Markdown 编辑器 -->
                <div v-else class="markdown-editor">
                  <div class="editor-toolbar">
                    <el-button-group>
                      <el-button size="small" title="粗体" @click="insertMarkdown('**', '**')">
                        <b>B</b>
                      </el-button>
                      <el-button size="small" title="斜体" @click="insertMarkdown('*', '*')">
                        <i>I</i>
                      </el-button>
                      <el-button size="small" title="删除线" @click="insertMarkdown('~~', '~~')">
                        <s>S</s>
                      </el-button>
                    </el-button-group>

                    <el-button-group>
                      <el-button size="small" title="标题1" @click="insertMarkdown('# ', '')"
                        >H1</el-button
                      >
                      <el-button size="small" title="标题2" @click="insertMarkdown('## ', '')"
                        >H2</el-button
                      >
                      <el-button size="small" title="标题3" @click="insertMarkdown('### ', '')"
                        >H3</el-button
                      >
                    </el-button-group>

                    <el-button-group>
                      <el-button size="small" title="无序列表" @click="insertMarkdown('- ', '')">
                        <i class="el-icon-menu"></i>
                      </el-button>
                      <el-button size="small" title="有序列表" @click="insertMarkdown('1. ', '')">
                        <i class="el-icon-tickets"></i>
                      </el-button>
                      <el-button size="small" title="链接" @click="insertMarkdown('[', '](url)')">
                        <i class="el-icon-link"></i>
                      </el-button>
                    </el-button-group>

                    <el-button-group>
                      <el-button size="small" title="图片" @click="insertMarkdown('![alt](', ')')">
                        <i class="el-icon-picture"></i>
                      </el-button>
                      <el-button
                        size="small"
                        title="代码块"
                        @click="insertMarkdown('```\n', '\n```')"
                      >
                        <i class="el-icon-document-copy"></i>
                      </el-button>
                      <el-button size="small" title="行内代码" @click="insertMarkdown('`', '`')">
                        <i class="el-icon-document"></i>
                      </el-button>
                    </el-button-group>
                  </div>

                  <div class="markdown-container">
                    <div class="markdown-edit-area">
                      <el-input
                        ref="markdownTextarea"
                        v-model="articleForm.content"
                        type="textarea"
                        placeholder="使用 Markdown 语法开始写作..."
                        class="markdown-textarea"
                        :autosize="false"
                      >
                      </el-input>
                    </div>
                    <div class="markdown-preview-area">
                      <div class="markdown-preview" v-html="markdownPreview"></div>
                    </div>
                  </div>
                </div>

                <div class="content-stats">
                  <span class="word-count">字数统计: {{ wordCount }}</span>
                  <span class="char-count">字符数: {{ charCount }}</span>
                </div>
              </el-card>
            </div>
          </el-col>

          <!-- 侧边栏 -->
          <el-col :lg="8" :md="24">
            <div class="editor-sidebar">
              <!-- 发布设置 -->
              <el-card class="publish-settings">
                <div slot="header">
                  <i class="el-icon-setting"></i>
                  发布设置
                </div>

                <el-form label-width="80px">
                  <el-form-item label="发布状态">
                    <el-radio-group v-model="articleForm.status">
                      <el-radio label="draft">草稿</el-radio>
                      <el-radio label="published">已发布</el-radio>
                      <el-radio label="private">私有</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="定时发布">
                    <el-switch v-model="enableSchedule"></el-switch>
                  </el-form-item>

                  <el-form-item v-if="enableSchedule" label="发布时间">
                    <el-date-picker
                      v-model="articleForm.scheduledAt"
                      type="datetime"
                      placeholder="选择发布时间"
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>

                  <el-form-item label="允许评论">
                    <el-switch v-model="articleForm.allowComments"></el-switch>
                  </el-form-item>

                  <el-form-item label="置顶文章">
                    <el-switch v-model="articleForm.featured"></el-switch>
                  </el-form-item>
                </el-form>
              </el-card>

              <!-- SEO 设置 -->
              <el-card class="seo-settings">
                <div slot="header">
                  <i class="el-icon-search"></i>
                  SEO 优化
                </div>

                <el-form label-width="80px">
                  <el-form-item label="SEO标题">
                    <el-input
                      v-model="articleForm.seoTitle"
                      placeholder="留空将使用文章标题"
                      maxlength="60"
                      show-word-limit
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="META描述">
                    <el-input
                      v-model="articleForm.metaDescription"
                      type="textarea"
                      placeholder="搜索引擎描述，建议120-160字符"
                      :rows="3"
                      maxlength="160"
                      show-word-limit
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="自定义URL">
                    <el-input v-model="articleForm.slug" placeholder="留空将自动生成"> </el-input>
                  </el-form-item>
                </el-form>

                <div class="seo-preview">
                  <h4>搜索预览</h4>
                  <div class="search-result-preview">
                    <div class="search-title">
                      {{ articleForm.seoTitle || articleForm.title || '文章标题' }}
                    </div>
                    <div class="search-url">
                      https://yourblog.com/{{ articleForm.slug || 'your-post' }}
                    </div>
                    <div class="search-description">
                      {{ articleForm.metaDescription || articleForm.excerpt || '文章描述...' }}
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 文章统计 -->
              <el-card class="article-stats">
                <div slot="header">
                  <i class="el-icon-data-line"></i>
                  文章统计
                </div>

                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-number">{{ wordCount }}</div>
                    <div class="stat-label">字数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">
                      {{ articleForm.readTime || 'auto' }}
                    </div>
                    <div class="stat-label">阅读时间</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ articleForm.tags.length }}</div>
                    <div class="stat-label">标签数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ imageCount }}</div>
                    <div class="stat-label">图片数</div>
                  </div>
                </div>

                <div v-if="lastSavedTime" class="last-saved">
                  <i class="el-icon-time"></i>
                  最后保存: {{ formatTime(lastSavedTime) }}
                </div>
              </el-card>

              <!-- 快速操作 -->
              <el-card class="quick-actions">
                <div slot="header">
                  <i class="el-icon-magic-stick"></i>
                  快速操作
                </div>

                <div class="action-buttons">
                  <el-button
                    icon="el-icon-magic-stick"
                    size="small"
                    block
                    @click="autoGenerateExcerpt"
                  >
                    自动生成摘要
                  </el-button>
                  <el-button
                    icon="el-icon-collection-tag"
                    size="small"
                    block
                    @click="autoGenerateTags"
                  >
                    智能推荐标签
                  </el-button>
                  <el-button icon="el-icon-view" size="small" block @click="checkGrammar">
                    语法检查
                  </el-button>
                  <el-button icon="el-icon-download" size="small" block @click="exportMarkdown">
                    导出 Markdown
                  </el-button>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog title="插入图片" :visible.sync="imageDialogVisible" width="500px">
      <el-tabs v-model="imageTabActive">
        <el-tab-pane label="上传图片" name="upload">
          <el-upload
            class="image-uploader"
            :action="uploadUrl"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :on-success="handleImageSuccess"
            :headers="uploadHeaders"
            accept="image/*"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
          </el-upload>
        </el-tab-pane>

        <el-tab-pane label="图片链接" name="url">
          <el-form>
            <el-form-item label="图片地址">
              <el-input v-model="imageUrl" placeholder="请输入图片URL"></el-input>
            </el-form-item>
            <el-form-item label="替代文本">
              <el-input v-model="imageAlt" placeholder="请输入图片描述"></el-input>
            </el-form-item>
          </el-form>
          <div class="dialog-footer">
            <el-button @click="imageDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="insertImageFromUrl">插入</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      title="文章预览"
      :visible.sync="previewVisible"
      width="80%"
      top="5vh"
      custom-class="preview-dialog"
    >
      <div class="article-preview">
        <div class="preview-header">
          <h1>{{ articleForm.title }}</h1>
          <div class="preview-meta">
            <span><i class="el-icon-time"></i> {{ formatDate(new Date()) }}</span>
            <span><i class="el-icon-view"></i> 预览模式</span>
            <span><i class="el-icon-folder"></i> {{ articleForm.category }}</span>
          </div>
          <div v-if="articleForm.tags.length" class="preview-tags">
            <el-tag v-for="tag in articleForm.tags" :key="tag" size="small">{{ tag }}</el-tag>
          </div>
        </div>

        <div class="preview-content" v-html="previewContent"></div>
      </div>

      <div slot="footer">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="publishFromPreview">发布文章</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 模拟 markdown 转换器（实际项目中应使用 marked 或类似库）
function markdownToHtml(markdown) {
  return markdown
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/~~(.*?)~~/g, '<del>$1</del>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>');
}

export default {
  name: 'BlogEditor',
  data() {
    return {
      isEdit: false,
      saving: false,
      publishing: false,
      lastSavedTime: null,

      // 编辑器模式
      editorMode: 'wysiwyg', // wysiwyg | markdown

      // 分别存储两种编辑器的内容
      wysiwygContent: '',
      markdownContent: '',

      // 用于实时统计的纯文本内容
      wysiwygTextContent: '',

      // 定时器用于实时更新字数统计
      updateTimer: null,

      // 表单数据
      articleForm: {
        title: '',
        excerpt: '',
        content: '',
        category: '',
        tags: [],
        coverImage: '',
        status: 'draft',
        allowComments: true,
        featured: false,
        readTime: null,
        scheduledAt: null,
        seoTitle: '',
        metaDescription: '',
        slug: '',
      },

      // 分类选项
      categories: [
        { value: '技术', label: '技术分享', icon: 'el-icon-cpu' },
        { value: '生活', label: '生活感悟', icon: 'el-icon-coffee' },
        { value: '学习', label: '学习笔记', icon: 'el-icon-reading' },
        { value: '工具', label: '工具推荐', icon: 'el-icon-suitcase' },
        { value: '其他', label: '其他内容', icon: 'el-icon-more' },
      ],

      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入文章标题', trigger: 'blur' },
          {
            min: 5,
            max: 100,
            message: '标题长度在 5 到 100 个字符',
            trigger: 'blur',
          },
        ],
        excerpt: [
          { required: true, message: '请输入文章摘要', trigger: 'blur' },
          {
            min: 50,
            max: 500,
            message: '摘要长度在 50 到 500 个字符',
            trigger: 'blur',
          },
        ],
        category: [{ required: true, message: '请选择文章分类', trigger: 'change' }],
      },

      // 标签输入
      tagInputVisible: false,
      tagInputValue: '',

      // 其他设置
      enableSchedule: false,

      // 对话框状态
      imageDialogVisible: false,
      previewVisible: false,
      imageTabActive: 'upload',
      imageUrl: '',
      imageAlt: '',

      // 上传配置
      uploadUrl: '/api/upload/image',
      uploadHeaders: {
        // Authorization: 'Bearer ' + token
      },
    };
  },

  computed: {
    // 字数统计
    wordCount() {
      const content =
        this.editorMode === 'wysiwyg' ? this.wysiwygTextContent : this.articleForm.content;
      return content.replace(/\s/g, '').length;
    },

    // 字符统计
    charCount() {
      const content =
        this.editorMode === 'wysiwyg' ? this.wysiwygTextContent : this.articleForm.content;
      return content.length;
    },

    // 图片数量统计
    imageCount() {
      const content = this.articleForm.content;
      const matches = content.match(/<img|!$$.*?$$$.*?$/g);
      return matches ? matches.length : 0;
    },

    // Markdown 预览
    markdownPreview() {
      if (this.editorMode === 'markdown') {
        return markdownToHtml(this.articleForm.content);
      }
      return '';
    },

    // 文章预览内容
    previewContent() {
      if (this.editorMode === 'wysiwyg') {
        return this.$refs.wysiwygEditor ? this.$refs.wysiwygEditor.innerHTML : '';
      } else {
        return this.markdownPreview;
      }
    },
  },

  watch: {
    // 监听编辑器模式变化
    editorMode: {
      handler(newMode) {
        if (newMode === 'markdown') {
          this.$nextTick(() => {
            this.initMarkdownEditor();
          });
        } else if (newMode === 'wysiwyg') {
          this.$nextTick(() => {
            this.initWysiwygEditor();
          });
        }
      },
      immediate: true,
    },

    // 监听文章内容变化，确保字数统计更新
    'articleForm.content': {
      handler() {
        // 强制触发计算属性重新计算
        this.$forceUpdate();
      },
    },
  },

  created() {
    // 检查是否为编辑模式
    if (this.$route.params.id) {
      this.isEdit = true;
      this.loadArticle(this.$route.params.id);
    }

    // 自动保存
    this.setupAutoSave();

    // 添加窗口大小变化监听
    window.addEventListener('resize', this.adjustMarkdownEditorHeight);
  },

  mounted() {
    // 初始化富文本编辑器
    this.initWysiwygEditor();

    // 监听页面离开
    window.addEventListener('beforeunload', this.handleBeforeUnload);

    // 确保 Markdown 编辑器正确初始化
    this.$nextTick(() => {
      this.initMarkdownEditor();
    });

    // 启动定时器来实时更新字数统计
    this.startUpdateTimer();
  },

  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    window.removeEventListener('resize', this.adjustMarkdownEditorHeight);
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    // 清理字数统计定时器
    this.stopUpdateTimer();
  },

  methods: {
    // 添加一个方法来调整 Markdown 编辑器的高度
    adjustMarkdownEditorHeight() {
      this.$nextTick(() => {
        if (this.$refs.markdownTextarea) {
          const textarea = this.$refs.markdownTextarea.$el.querySelector('textarea');
          if (textarea) {
            // 获取容器高度
            const containerHeight = this.$el.querySelector('.markdown-container').clientHeight;
            // 设置文本区域高度
            textarea.style.height = `${containerHeight}px`;
            textarea.style.minHeight = '500px';
          }
        }
      });
    },
    // 加载文章数据（编辑模式）
    async loadArticle(id) {
      try {
        // 模拟 API 调用
        const response = await this.$http.get(`/api/articles/${id}`);
        this.articleForm = { ...response.data };
      } catch (error) {
        this.$message.error('加载文章失败');
      }
    },

    // 设置自动保存
    setupAutoSave() {
      this.autoSaveTimer = setInterval(() => {
        if (this.hasUnsavedChanges()) {
          this.saveDraft(true); // 静默保存
        }
      }, 30000); // 每30秒自动保存
    },

    // 检查是否有未保存的更改
    hasUnsavedChanges() {
      // 实现更改检测逻辑
      return this.articleForm.title || this.articleForm.content;
    },

    // 页面离开前提醒
    handleBeforeUnload(event) {
      if (this.hasUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = '';
        return '';
      }
    },

    // 保存草稿
    async saveDraft(silent = false) {
      if (!silent) this.saving = true;

      try {
        // 确保获取当前编辑器的最新内容
        this.ensureCurrentContent();

        const data = {
          ...this.articleForm,
          status: 'draft',
        };

        if (this.isEdit) {
          await this.$http.put(`/api/articles/${this.$route.params.id}`, data);
        } else {
          const response = await this.$http.post('/api/articles', data);
          // 如果是新文章，保存后跳转到编辑模式
          if (!this.isEdit) {
            this.isEdit = true;
            this.$router.replace(`/editor/${response.data.id}`);
          }
        }

        this.lastSavedTime = new Date();
        if (!silent) {
          this.$message.success('草稿已保存');
        }
      } catch (error) {
        if (!silent) {
          this.$message.error('保存失败');
        }
      } finally {
        if (!silent) this.saving = false;
      }
    },

    // 发布文章
    async publishArticle() {
      // 表单验证
      try {
        await this.$refs.articleForm.validate();
      } catch (error) {
        this.$message.error('请完善必填信息');
        return;
      }

      if (!this.articleForm.content.trim()) {
        this.$message.error('请输入文章内容');
        return;
      }

      this.publishing = true;

      try {
        const data = {
          ...this.articleForm,
          status: 'published',
          publishedAt: new Date(),
        };

        if (this.isEdit) {
          await this.$http.put(`/api/articles/${this.$route.params.id}`, data);
          this.$message.success('文章已更新');
        } else {
          const response = await this.$http.post('/api/articles', data);
          this.$message.success('文章已发布');
          this.$router.replace(`/blog/${response.data.id}`);
        }
      } catch (error) {
        this.$message.error('发布失败');
      } finally {
        this.publishing = false;
      }
    },

    // 预览文章
    previewArticle() {
      if (!this.articleForm.title.trim()) {
        this.$message.error('请输入文章标题');
        return;
      }

      if (!this.articleForm.content.trim()) {
        this.$message.error('请输入文章内容');
        return;
      }

      this.previewVisible = true;
    },

    // 从预览页面发布
    publishFromPreview() {
      this.previewVisible = false;
      this.publishArticle();
    },

    // 切换编辑器模式
    switchEditorMode(mode) {
      if (mode === this.editorMode) return;

      if (mode === 'markdown' && this.editorMode === 'wysiwyg') {
        // 从富文本切换到Markdown
        // 使用当前存储的wysiwygContent，避免重复获取
        const content =
          this.wysiwygContent ||
          (this.$refs.wysiwygEditor ? this.$refs.wysiwygEditor.innerHTML : '');

        // 转换HTML到Markdown
        if (content && content.trim()) {
          this.markdownContent = this.htmlToMarkdown(content);
          this.articleForm.content = this.markdownContent;
        } else {
          // 如果没有HTML内容，使用纯文本内容
          this.articleForm.content = this.wysiwygTextContent || '';
        }
      } else if (mode === 'wysiwyg' && this.editorMode === 'markdown') {
        // 从Markdown切换到富文本
        this.markdownContent = this.articleForm.content;

        this.$nextTick(() => {
          if (this.$refs.wysiwygEditor) {
            const htmlContent = markdownToHtml(this.markdownContent);
            this.$refs.wysiwygEditor.innerHTML = htmlContent;
            this.wysiwygContent = htmlContent;
            // 更新文本内容用于字数统计
            this.wysiwygTextContent =
              this.$refs.wysiwygEditor.innerText || this.$refs.wysiwygEditor.textContent || '';
          }
        });
      }

      this.editorMode = mode;

      // 确保切换后编辑器能正确初始化和获得焦点
      this.$nextTick(() => {
        if (mode === 'markdown') {
          this.initMarkdownEditor();
          setTimeout(() => {
            if (this.$refs.markdownTextarea) {
              const textarea = this.$refs.markdownTextarea.$el.querySelector('textarea');
              if (textarea) {
                textarea.focus();
              }
            }
          }, 200);
        } else if (mode === 'wysiwyg' && this.$refs.wysiwygEditor) {
          this.$refs.wysiwygEditor.focus();
        }
      });
    },

    // 初始化富文本编辑器
    initWysiwygEditor() {
      this.$nextTick(() => {
        if (this.$refs.wysiwygEditor) {
          this.$refs.wysiwygEditor.addEventListener('input', this.handleWysiwygInput);
          this.$refs.wysiwygEditor.addEventListener('keydown', this.handleWysiwygKeydown);
          this.$refs.wysiwygEditor.addEventListener('keyup', this.handleWysiwygInput);
          this.$refs.wysiwygEditor.addEventListener('paste', this.handleWysiwygInput);

          // 初始化文本内容
          this.wysiwygTextContent =
            this.$refs.wysiwygEditor.innerText || this.$refs.wysiwygEditor.textContent || '';
        }
      });
    },

    // 初始化Markdown编辑器
    initMarkdownEditor() {
      if (this.$refs.markdownTextarea) {
        const textarea = this.$refs.markdownTextarea.$el.querySelector('textarea');
        if (textarea) {
          // 确保文本区域可以正常输入
          textarea.style.height = '100%';
          textarea.style.minHeight = '500px';
          textarea.style.resize = 'none';
          textarea.style.border = 'none';
          textarea.style.outline = 'none';
          textarea.style.padding = '15px';
          textarea.style.fontFamily = "'Monaco', 'Menlo', 'Ubuntu Mono', monospace";
          textarea.style.fontSize = '14px';
          textarea.style.lineHeight = '1.6';
          textarea.style.backgroundColor = '#fff';
          textarea.style.color = '#333';

          // 确保可以获得焦点和输入
          textarea.removeAttribute('readonly');
          textarea.removeAttribute('disabled');
          // 移除contenteditable，因为textarea不需要这个属性

          // 确保没有阻止输入的事件监听器
          textarea.style.pointerEvents = 'auto';
          textarea.style.userSelect = 'text';
          textarea.style.webkitUserSelect = 'text';
          textarea.style.mozUserSelect = 'text';
          textarea.style.msUserSelect = 'text';

          // 移除可能阻止输入的事件监听器
          textarea.onkeydown = null;
          textarea.onkeypress = null;
          textarea.onkeyup = null;
          textarea.oninput = null;

          // 确保输入法可以正常工作
          textarea.style.imeMode = 'auto';
          textarea.setAttribute('autocomplete', 'off');
          textarea.setAttribute('spellcheck', 'false');

          // 强制设置为可编辑状态
          textarea.readOnly = false;
          textarea.disabled = false;
          textarea.setAttribute('tabindex', '0');

          // 强制刷新元素状态
          setTimeout(() => {
            textarea.blur();
            textarea.focus();
          }, 100);
        }
      }
    },

    // 富文本编辑器输入处理
    handleWysiwygInput(event) {
      // 只在富文本模式下保存内容，避免重复
      if (this.editorMode === 'wysiwyg') {
        this.wysiwygContent = event.target.innerHTML;
        // 更新纯文本内容用于字数统计
        this.wysiwygTextContent = event.target.innerText || event.target.textContent || '';
        // 注意：不在这里更新articleForm.content，避免与模式切换时的逻辑冲突
      }
    },

    // 富文本编辑器键盘事件
    handleWysiwygKeydown(event) {
      // 处理快捷键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'b':
            event.preventDefault();
            this.execCommand('bold');
            break;
          case 'i':
            event.preventDefault();
            this.execCommand('italic');
            break;
          case 'u':
            event.preventDefault();
            this.execCommand('underline');
            break;
          case 's':
            event.preventDefault();
            this.saveDraft();
            break;
        }
      }
    },

    // 执行富文本编辑命令
    execCommand(command, value = null) {
      document.execCommand(command, false, value);
      this.$refs.wysiwygEditor.focus();
      // 执行命令后更新文本内容
      this.updateWysiwygTextContent();
    },

    // 更新富文本编辑器的文本内容
    updateWysiwygTextContent() {
      if (this.$refs.wysiwygEditor && this.editorMode === 'wysiwyg') {
        this.wysiwygTextContent =
          this.$refs.wysiwygEditor.innerText || this.$refs.wysiwygEditor.textContent || '';
        this.wysiwygContent = this.$refs.wysiwygEditor.innerHTML;
        // 注意：不在这里更新articleForm.content，避免与模式切换时的逻辑冲突
      }
    },

    // 启动定时器来实时更新字数统计
    startUpdateTimer() {
      this.updateTimer = setInterval(() => {
        if (this.editorMode === 'wysiwyg' && this.$refs.wysiwygEditor) {
          const currentText =
            this.$refs.wysiwygEditor.innerText || this.$refs.wysiwygEditor.textContent || '';
          if (currentText !== this.wysiwygTextContent) {
            this.wysiwygTextContent = currentText;
            this.wysiwygContent = this.$refs.wysiwygEditor.innerHTML;
            // 注意：不在这里更新articleForm.content，避免与模式切换时的逻辑冲突
          }
        }
      }, 500); // 每500ms检查一次
    },

    // 停止定时器
    stopUpdateTimer() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer);
        this.updateTimer = null;
      }
    },

    // 确保获取当前编辑器的正确内容
    ensureCurrentContent() {
      if (this.editorMode === 'wysiwyg' && this.$refs.wysiwygEditor) {
        // 在富文本模式下，确保内容是最新的
        this.wysiwygContent = this.$refs.wysiwygEditor.innerHTML;
        this.wysiwygTextContent =
          this.$refs.wysiwygEditor.innerText || this.$refs.wysiwygEditor.textContent || '';

        // 如果富文本内容不为空，将其转换为最终内容
        if (this.wysiwygContent && this.wysiwygContent.trim()) {
          // 如果只是纯文本（没有HTML标签），直接使用文本内容
          if (!this.wysiwygContent.includes('<') || !this.wysiwygContent.includes('>')) {
            this.articleForm.content = this.wysiwygTextContent;
          } else {
            this.articleForm.content = this.wysiwygContent;
          }
        }
      }
      // Markdown模式下，articleForm.content已经是最新的
    },

    // 插入标题
    insertHeading(tag) {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const heading = document.createElement(tag);
        heading.textContent = range.toString() || '标题文本';
        range.deleteContents();
        range.insertNode(heading);
        selection.removeAllRanges();
        selection.addRange(range);
        // 更新文本内容
        this.updateWysiwygTextContent();
      }
    },

    // 插入链接
    insertLink() {
      const url = prompt('请输入链接地址:');
      if (url) {
        this.execCommand('createLink', url);
        // execCommand已经会调用updateWysiwygTextContent
      }
    },

    // 插入图片
    insertImage() {
      this.imageDialogVisible = true;
    },

    // 插入代码块
    insertCode() {
      const code = prompt('请输入代码:');
      if (code) {
        const pre = document.createElement('pre');
        const codeEl = document.createElement('code');
        codeEl.textContent = code;
        pre.appendChild(codeEl);

        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          range.insertNode(pre);
          // 更新文本内容
          this.updateWysiwygTextContent();
        }
      }
    },

    // Markdown 插入文本
    insertMarkdown(before, after) {
      const textarea = this.$refs.markdownTextarea.$el.querySelector('textarea');
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = textarea.value.substring(start, end);

      let newText = before + selectedText + after;

      // 如果是换行符开头的语法，且不在行首，则添加换行
      if (before.includes('\n') || before.includes('# ') || before.includes('- ')) {
        if (start > 0 && textarea.value[start - 1] !== '\n') {
          newText = '\n' + newText;
        }
      }

      const beforeText = textarea.value.substring(0, start);
      const afterText = textarea.value.substring(end);

      this.articleForm.content = beforeText + newText + afterText;

      // 重新设置光标位置
      this.$nextTick(() => {
        const newCursorPos = start + before.length + selectedText.length;
        textarea.focus();
        textarea.setSelectionRange(newCursorPos, newCursorPos);
      });
    },

    // 处理粘贴事件
    handlePaste(event) {
      event.preventDefault();
      const clipboardData = event.clipboardData || window.clipboardData;
      const text = clipboardData.getData('text/plain');

      // 检查是否为图片
      const items = clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          const file = items[i].getAsFile();
          this.uploadImageFile(file);
          return;
        }
      }

      // 插入纯文本
      document.execCommand('insertText', false, text);
      // 更新文本内容
      setTimeout(() => {
        this.updateWysiwygTextContent();
      }, 10);
    },

    // 标签操作
    showTagInput() {
      this.tagInputVisible = true;
      this.$nextTick(() => {
        this.$refs.tagInput.focus();
      });
    },

    addTag() {
      const tag = this.tagInputValue.trim();
      if (tag && !this.articleForm.tags.includes(tag)) {
        this.articleForm.tags.push(tag);
      }
      this.tagInputVisible = false;
      this.tagInputValue = '';
    },

    removeTag(tag) {
      const index = this.articleForm.tags.indexOf(tag);
      if (index > -1) {
        this.articleForm.tags.splice(index, 1);
      }
    },

    // 封面图片上传
    beforeCoverUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('只能上传图片格式!');
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!');
      }

      return isImage && isLt2M;
    },

    handleCoverSuccess(response) {
      this.articleForm.coverImage = response.data.url;
      this.$message.success('封面上传成功');
    },

    handleUploadError(error) {
      this.$message.error('上传失败');
    },

    removeCover() {
      this.articleForm.coverImage = '';
    },

    // 内容图片上传
    beforeImageUpload(file) {
      return this.beforeCoverUpload(file);
    },

    handleImageSuccess(response) {
      this.insertImageFromUrl(response.data.url, this.imageAlt);
      this.imageDialogVisible = false;
    },

    // 从URL插入图片
    insertImageFromUrl(url = this.imageUrl, alt = this.imageAlt) {
      if (!url) {
        this.$message.error('请输入图片地址');
        return;
      }

      if (this.editorMode === 'wysiwyg') {
        const img = document.createElement('img');
        img.src = url;
        img.alt = alt || '图片';
        img.style.maxWidth = '100%';

        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          range.insertNode(img);
        }
      } else {
        const markdown = `![${alt || '图片'}](${url})`;
        this.insertMarkdown(markdown, '');
      }

      this.imageUrl = '';
      this.imageAlt = '';
    },

    // 上传图片文件
    async uploadImageFile(file) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await this.$http.post(this.uploadUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            ...this.uploadHeaders,
          },
        });

        this.insertImageFromUrl(response.data.url, '粘贴的图片');
      } catch (error) {
        this.$message.error('图片上传失败');
      }
    },

    // 智能功能
    autoGenerateExcerpt() {
      if (!this.articleForm.content.trim()) {
        this.$message.error('请先输入文章内容');
        return;
      }

      const text = this.articleForm.content.replace(/<[^>]*>/g, '').trim();
      const excerpt = text.substring(0, 150) + (text.length > 150 ? '...' : '');
      this.articleForm.excerpt = excerpt;
      this.$message.success('摘要已自动生成');
    },

    autoGenerateTags() {
      if (!this.articleForm.content.trim()) {
        this.$message.error('请先输入文章内容');
        return;
      }

      // 简单的关键词提取（实际项目中可以使用更复杂的算法）
      const keywords = ['Vue', 'JavaScript', 'CSS', 'HTML', '前端', '后端', '开发', '教程'];
      const content = this.articleForm.content.toLowerCase();
      const suggestedTags = keywords.filter(
        (keyword) =>
          content.includes(keyword.toLowerCase()) && !this.articleForm.tags.includes(keyword)
      );

      if (suggestedTags.length > 0) {
        this.articleForm.tags = [...this.articleForm.tags, ...suggestedTags.slice(0, 3)];
        this.$message.success(`已添加推荐标签: ${suggestedTags.slice(0, 3).join(', ')}`);
      } else {
        this.$message.info('未找到合适的推荐标签');
      }
    },

    checkGrammar() {
      // 简单的语法检查
      const issues = [];
      const content = this.articleForm.content;

      // 检查常见问题
      if (content.includes('，，')) {
        issues.push('发现连续标点符号');
      }
      if (content.includes('  ')) {
        issues.push('发现多余空格');
      }

      if (issues.length > 0) {
        this.$message.warning(`发现 ${issues.length} 个潜在问题`);
      } else {
        this.$message.success('未发现语法问题');
      }
    },

    exportMarkdown() {
      let content = this.articleForm.content;

      if (this.editorMode === 'wysiwyg') {
        content = this.htmlToMarkdown(content);
      }

      const markdown = `# ${this.articleForm.title}\n\n${content}`;

      // 创建下载链接
      const blob = new Blob([markdown], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${this.articleForm.title || 'article'}.md`;
      a.click();
      URL.revokeObjectURL(url);
    },

    // HTML转Markdown（简化版）
    htmlToMarkdown(html) {
      if (!html || typeof html !== 'string') {
        return '';
      }

      // 如果内容不包含HTML标签，直接返回
      if (!html.includes('<') || !html.includes('>')) {
        return html;
      }

      return html
        .replace(/<h1>(.*?)<\/h1>/g, '# $1')
        .replace(/<h2>(.*?)<\/h2>/g, '## $1')
        .replace(/<h3>(.*?)<\/h3>/g, '### $1')
        .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
        .replace(/<b>(.*?)<\/b>/g, '**$1**')
        .replace(/<em>(.*?)<\/em>/g, '*$1*')
        .replace(/<i>(.*?)<\/i>/g, '*$1*')
        .replace(/<del>(.*?)<\/del>/g, '~~$1~~')
        .replace(/<code>(.*?)<\/code>/g, '`$1`')
        .replace(/<br\s*\/?>/g, '\n')
        .replace(/<p>(.*?)<\/p>/g, '$1\n\n')
        .replace(/<div>(.*?)<\/div>/g, '$1\n')
        .replace(/<[^>]*>/g, '')
        .replace(/\n\n+/g, '\n\n')
        .trim();
    },

    // 工具函数
    formatTime(time) {
      return new Date(time).toLocaleString();
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString();
    },
  },
};
</script>

<style scoped>
.blog-editor {
  background: #f8f9fa;
  min-height: 100vh;
  /* 添加以下属性确保内容可滚动 */
  position: relative;
  overflow-y: auto;
}

/* 头部样式 */
.editor-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  /* 如果头部是固定的，添加以下代码 */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  margin: 0 0 10px;
  font-weight: 700;
}

.page-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.action-buttons .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.action-buttons .el-button.el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

/* 确保编辑器容器不限制滚动 */
.editor-container {
  padding: 30px 0;
  overflow: visible;
  margin-top: 100px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.el-card >>> .el-card__header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
  font-weight: 600;
  border-radius: 10px 10px 0 0;
}

.el-card >>> .el-card__body {
  padding: 20px;
}

/* 标签输入样式 */
.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  margin: 0;
}

.tag-input {
  width: 80px;
}

.add-tag-btn {
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
}

.add-tag-btn:hover {
  color: #409eff;
  border-color: #409eff;
}

/* 封面上传样式 */
.cover-upload {
  text-align: center;
}

.cover-uploader >>> .el-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.cover-uploader >>> .el-upload:hover {
  border-color: #409eff;
}

.cover-preview {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.cover-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cover-preview:hover .cover-overlay {
  opacity: 1;
}

.upload-placeholder {
  padding: 40px;
  text-align: center;
}

.upload-placeholder i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 20px;
}

.upload-text p {
  margin: 0;
  color: #666;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

.cover-actions {
  margin-top: 15px;
}

/* 编辑器样式 */
.editor-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-tools {
  display: flex;
  align-items: center;
  gap: 10px;
}

.editor-toolbar {
  display: flex;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.wysiwyg-content {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: white;
  font-size: 14px;
  line-height: 1.8;
}

.wysiwyg-content:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

.wysiwyg-content:empty:before {
  content: attr(placeholder);
  color: #c0c4cc;
  pointer-events: none;
}

/* 修改固定高度的容器，确保它们有适当的溢出处理 */
.markdown-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.markdown-container {
  display: flex;
  flex-direction: row;
  height: 600px;
  min-height: 500px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.markdown-edit-area {
  width: 50%;
  border-right: 1px solid #e9ecef;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.markdown-preview-area {
  width: 50%;
  height: 100%;
  overflow-y: auto;
}

.markdown-textarea {
  height: 100%;
  flex: 1;
}

.markdown-textarea >>> .el-textarea__inner {
  height: 100% !important;
  min-height: 500px !important;
  border: none;
  border-radius: 0;
  resize: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  padding: 15px;
  background: #fff !important;
  color: #333 !important;
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  /* 确保输入法和特殊字符输入正常工作 */
  ime-mode: auto !important;
  -webkit-ime-mode: auto !important;
  -moz-ime-mode: auto !important;
  -ms-ime-mode: auto !important;
  /* 移除可能阻止输入的样式 */
  -webkit-touch-callout: default !important;
  -webkit-user-modify: read-write !important;
}

.markdown-textarea >>> .el-textarea__inner:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  background: #fff !important;
}

/* 确保textarea元素本身也没有阻止输入的样式 */
.markdown-textarea >>> textarea {
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.markdown-preview {
  height: 100%;
  padding: 20px;
  background: #fafafa;
  font-size: 14px;
  line-height: 1.8;
  overflow-y: auto;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3 {
  margin: 20px 0 15px;
  font-weight: 600;
  color: #333;
}

.markdown-preview h1 {
  font-size: 24px;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

.markdown-preview h2 {
  font-size: 20px;
}

.markdown-preview h3 {
  font-size: 16px;
}

.markdown-preview p {
  margin-bottom: 15px;
  color: #555;
}

.markdown-preview code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.markdown-preview pre {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 15px 0;
}

.content-stats {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 12px;
}

/* 侧边栏样式 */
.editor-sidebar {
  position: sticky;
  top: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.last-saved {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 12px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-buttons .el-button {
  justify-content: flex-start;
}

/* SEO 预览样式 */
.seo-preview {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.seo-preview h4 {
  margin: 0 0 15px;
  font-size: 14px;
  color: #333;
}

.search-result-preview {
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.search-title {
  color: #1a0dab;
  font-size: 18px;
  margin-bottom: 5px;
  text-decoration: underline;
}

.search-url {
  color: #006621;
  font-size: 14px;
  margin-bottom: 8px;
}

.search-description {
  color: #545454;
  font-size: 13px;
  line-height: 1.4;
}

/* 预览对话框样式 */
.preview-dialog {
  max-width: 90%;
}

.preview-dialog >>> .el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
}

.article-preview {
  padding: 30px;
}

.preview-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.preview-header h1 {
  font-size: 2.5rem;
  margin: 0 0 15px;
  color: #333;
}

.preview-meta {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

.preview-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-tags {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-content {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  max-width: 800px;
  margin: 0 auto;
}

.preview-content h1,
.preview-content h2,
.preview-content h3 {
  margin: 30px 0 15px;
  font-weight: 600;
}

.preview-content p {
  margin-bottom: 15px;
}

.preview-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 20px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .editor-container {
    padding: 20px 0;
  }

  .page-title {
    font-size: 2rem;
  }

  .editor-toolbar {
    gap: 5px;
  }

  .editor-toolbar .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }

  /* 在小屏幕上调整 markdown 容器，但保持水平布局 */
  .markdown-container {
    height: 400px; /* 在小屏幕上减小高度 */
    flex-direction: row !important; /* 强制保持水平布局 */
  }

  /* 确保在小屏幕上文本区域和预览区域都有合适的高度 */
  .markdown-textarea >>> .el-textarea__inner {
    min-height: 400px !important; /* 在小屏幕上减小最小高度 */
  }

  .markdown-preview {
    height: 400px; /* 在小屏幕上设置固定高度 */
  }

  .wysiwyg-content {
    min-height: 400px;
    max-height: 600px; /* 这个最大高度可能限制了内容 */
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    background: white;
    font-size: 14px;
    line-height: 1.8;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .editor-toolbar {
    flex-direction: column;
    gap: 10px;
  }

  .el-card >>> .el-card__body {
    padding: 15px;
  }
}

/* 动画效果 */
.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.el-button {
  transition: all 0.3s ease;
}

.tag-item {
  transition: all 0.3s ease;
}

.tag-item:hover {
  transform: scale(1.05);
}
</style>
